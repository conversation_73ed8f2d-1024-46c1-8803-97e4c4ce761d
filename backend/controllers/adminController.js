const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

// Retrieves a dashboard overview with user counts, revenue, and report stats.
// - Input: JWT in the Authorization header.
// - Output: JSON response with dashboard data, or an error message if the query fails.
const getDashboard = async (req, res) => {
  try {
    if (req.user.role !== "admin") return res.status(403).json({ error: "Unauthorized" });
    const userCount = await prisma.user.count();
    const revenue = await prisma.payment.aggregate({ _sum: { amount: true }, where: { status: "completed" } });
    const reportCount = await prisma.report.count();
    res.json({ userCount, revenue: revenue._sum.amount || 0, reportCount });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Assigns or removes admin/moderator roles for a user.
// - Input: JSON body with user_id, role ("admin" or "moderator"); JWT in the Authorization header.
// - Output: JSON response with a success message, or an error message if the update fails.
const manageRoles = async (req, res) => {
  const { user_id, role } = req.body;
  try {
    if (req.user.role !== "admin") return res.status(403).json({ error: "Unauthorized" });
    await prisma.user.update({
      where: { id: user_id },
      data: { role },
    });
    await prisma.auditLog.create({
      data: {
        actor_id: req.user.id,
        action: "manage_role",
        details: { user_id, role },
      },
    });
    res.json({ message: `Role updated to ${role}` });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Issues a refund for a payment.
// - Input: JSON body with payment_id; JWT in the Authorization header.
// - Output: JSON response with a success message, or an error message if the update fails.
const issueRefund = async (req, res) => {
  const { payment_id } = req.body;
  try {
    if (req.user.role !== "admin") return res.status(403).json({ error: "Unauthorized" });
    const payment = await prisma.payment.update({
      where: { id: payment_id },
      data: { status: "refunded" },
    });
    await prisma.auditLog.create({
      data: {
        actor_id: req.user.id,
        action: "issue_refund",
        details: { payment_id },
      },
    });
    res.json({ message: "Refund issued" });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Updates system policies or terms, storing versioned changes.
// - Input: JSON body with policy_type, content; JWT in the Authorization header.
// - Output: JSON response with a success message, or an error message if the update fails.
const updatePolicies = async (req, res) => {
  const { policy_type, content } = req.body;
  try {
    if (req.user.role !== "admin") return res.status(403).json({ error: "Unauthorized" });
    await prisma.auditLog.create({
      data: {
        actor_id: req.user.id,
        action: "update_policy",
        details: { policy_type, content },
      },
    });
    // In a real app, store policies in a separate table or external system
    await prisma.notification.createMany({
      data: await prisma.user.findMany().then((users) =>
        users.map((user) => ({
          recipient_id: user.id,
          type: "policy_update",
          message: `System ${policy_type} updated`,
          status: "sent",
        }))
      ),
    });
    res.json({ message: "Policy updated" });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Processes GDPR data export or deletion requests.
// - Input: JSON body with request_id; JWT in the Authorization header.
// - Output: JSON response with a success message, or an error message if the processing fails.
const processDataRequest = async (req, res) => {
  const { request_id } = req.body;
  try {
    if (req.user.role !== "admin") return res.status(403).json({ error: "Unauthorized" });
    const dataRequest = await prisma.dataRequest.update({
      where: { id: request_id },
      data: { status: "completed" },
    });
    if (dataRequest.type === "delete") {
      await prisma.user.update({
        where: { id: dataRequest.user_id },
        data: { status: "deleted" },
      });
    }
    await prisma.auditLog.create({
      data: {
        actor_id: req.user.id,
        action: `process_data_${dataRequest.type}`,
        details: { request_id },
      },
    });
    await prisma.notification.create({
      data: {
        recipient_id: dataRequest.user_id,
        type: "data_request_processed",
        message: `Your ${dataRequest.type} request has been completed`,
        status: "sent",
      },
    });
    res.json({ message: "Data request processed" });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Gets all pending teachers for admin review
// - Input: JWT in the Authorization header (admin role required)
// - Output: JSON response with list of pending teachers and their information
const getPendingTeachers = async (req, res) => {
  try {
    if (req.user.role !== "admin") return res.status(403).json({ error: "Unauthorized" });

    const pendingTeachers = await prisma.teacher.findMany({
      where: {
        verified_status: "pending",
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            created_at: true,
            language: true,
          },
        },
      },
      orderBy: {
        user: {
          created_at: "desc",
        },
      },
    });

    res.json(pendingTeachers);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Gets all approved teachers for admin review
// - Input: JWT in the Authorization header (admin role required)
// - Output: JSON response with list of approved teachers and their information
const getApprovedTeachers = async (req, res) => {
  try {
    if (req.user.role !== "admin") return res.status(403).json({ error: "Unauthorized" });

    const approvedTeachers = await prisma.teacher.findMany({
      where: {
        verified_status: "approved",
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            created_at: true,
            language: true,
          },
        },
      },
      orderBy: {
        user: {
          created_at: "desc",
        },
      },
    });

    res.json(approvedTeachers);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Approves or rejects a teacher application
// - Input: JSON body with teacher_id, status ("approved" or "rejected"), comments (optional); JWT in the Authorization header
// - Output: JSON response with success message, or error message if the update fails
const manageTeacherStatus = async (req, res) => {
  const { teacher_id, status, comments } = req.body;
  try {
    if (req.user.role !== "admin") return res.status(403).json({ error: "Unauthorized" });

    if (!["approved", "rejected"].includes(status)) {
      return res.status(400).json({ error: 'Invalid status. Must be "approved" or "rejected"' });
    }

    // Update teacher verified_status
    const teacher = await prisma.teacher.update({
      where: { id: teacher_id },
      data: { verified_status: status },
      include: { user: true },
    });

    // Update user status
    await prisma.user.update({
      where: { id: teacher.user_id },
      data: { status: status === "approved" ? "active" : "rejected" },
    });

    // Create audit log
    await prisma.auditLog.create({
      data: {
        actor_id: req.user.id,
        action: `teacher_${status}`,
        details: { teacher_id, comments },
      },
    });

    // Create notification for the teacher
    await prisma.notification.create({
      data: {
        recipient_id: teacher.user_id,
        type: "teacher_approval",
        message: `Your teacher application has been ${status}${comments ? `: ${comments}` : ""}`,
        status: "sent",
      },
    });

    res.json({ message: `Teacher ${status} successfully`, teacher: teacher });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

module.exports = { getDashboard, manageRoles, issueRefund, updatePolicies, processDataRequest, getPendingTeachers, getApprovedTeachers, manageTeacherStatus };
